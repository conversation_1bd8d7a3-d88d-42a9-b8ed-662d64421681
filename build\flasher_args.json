{"write_flash_args": ["--flash_mode", "dio", "--flash_size", "2MB", "--flash_freq", "40m"], "flash_settings": {"flash_mode": "dio", "flash_size": "2MB", "flash_freq": "40m"}, "flash_files": {"0x1000": "bootloader/bootloader.bin", "0x10000": "uart_echo.bin", "0x8000": "partition_table/partition-table.bin"}, "bootloader": {"offset": "0x1000", "file": "bootloader/bootloader.bin", "encrypted": "false"}, "app": {"offset": "0x10000", "file": "uart_echo.bin", "encrypted": "false"}, "partition-table": {"offset": "0x8000", "file": "partition_table/partition-table.bin", "encrypted": "false"}, "extra_esptool_args": {"after": "hard_reset", "before": "default_reset", "stub": true, "chip": "esp32"}}