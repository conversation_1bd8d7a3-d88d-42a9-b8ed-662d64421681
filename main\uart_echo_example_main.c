/* UART Echo Example

   This example code is in the Public Domain (or CC0 licensed, at your option.)

   Unless required by applicable law or agreed to in writing, this
   software is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR
   CONDITIONS OF ANY KIND, either express or implied.
*/
#include <stdio.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/uart.h"
#include "driver/gpio.h"
#include "sdkconfig.h"
#include "esp_log.h"
#include "fingerprint.h"

/**
 * This is an example which echos any data it receives on configured UART back to the sender,
 * with hardware flow control turned off. It does not use UART driver event queue.
 *
 * - Port: configured UART
 * - Receive (Rx) buffer: on
 * - Transmit (Tx) buffer: off
 * - Flow control: off
 * - Event queue: off
 * - Pin assignment: see defines below (See Kconfig)
 */

#define ECHO_TEST_TXD (CONFIG_EXAMPLE_UART_TXD)
#define ECHO_TEST_RXD (CONFIG_EXAMPLE_UART_RXD)
#define ECHO_TEST_RTS (UART_PIN_NO_CHANGE)
#define ECHO_TEST_CTS (UART_PIN_NO_CHANGE)

#define ECHO_UART_PORT_NUM      (CONFIG_EXAMPLE_UART_PORT_NUM)
#define ECHO_UART_BAUD_RATE     (CONFIG_EXAMPLE_UART_BAUD_RATE)
#define ECHO_TASK_STACK_SIZE    (CONFIG_EXAMPLE_TASK_STACK_SIZE)

static const char *TAG = "UART TEST";

#define BUF_SIZE (1024)
#define DATA_SIZE (16)  // Number of bytes to send/receive

//instantiate instance of Fingerprint_Packet here

struct Fingerprint_Packet packet = {
    .start_code = FINGERPRINT_STARTCODE,
    .type = FINGERPRINT_COMMANDPACKET,
    .length = 0x0007,
    .address = {0xFF, 0xFF, 0xFF, 0xFF},
    .data = {0x1, 0x0, 0x7, 0x35, 0x1, 0x64, 0x2, 0x0, 0x0, 0xA4}
};

//Create send_packet function
void send_packet(struct Fingerprint_Packet *packet) {
    uart_write_bytes(ECHO_UART_PORT_NUM, (const char *) packet, packet->length + 6);
}

//add a function here to calculate the packet checksum
uint8_t calculate_checksum(struct Fingerprint_Packet *packet) {
    printf("packet-.type: %02x\n", packet->type);
    printf("packet->length: %02x\n", packet->length);
    printf("packet->data: ");
    for (int i = 0; i < packet->length; i++) {
        printf("%02x ", packet->data[i]);
    }
    printf("\n");
    uint8_t checksum = packet->type; //type is 1 byte so we can just add it to the checksum here
    checksum += packet->length >> 8; //this line should be length high byte
    checksum += packet->length & 0xFF; //this line should be length low byte
    for (int i = 0; i < packet->length; i++) {
        checksum += packet->data[i];
    }
    return checksum;
}

static void echo_task(void *arg)
{
    /* Configure parameters of an UART driver,
     * communication pins and install the driver */
    uart_config_t uart_config = {
        .baud_rate = ECHO_UART_BAUD_RATE,
        .data_bits = UART_DATA_8_BITS,
        .parity    = UART_PARITY_DISABLE,
        .stop_bits = UART_STOP_BITS_1,
        .flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
        .source_clk = UART_SCLK_DEFAULT,
    };
    int intr_alloc_flags = 0;

#if CONFIG_UART_ISR_IN_IRAM
    intr_alloc_flags = ESP_INTR_FLAG_IRAM;
#endif

    ESP_ERROR_CHECK(uart_driver_install(ECHO_UART_PORT_NUM, BUF_SIZE * 2, 0, 0, NULL, intr_alloc_flags));
    ESP_ERROR_CHECK(uart_param_config(ECHO_UART_PORT_NUM, &uart_config));
    ESP_ERROR_CHECK(uart_set_pin(ECHO_UART_PORT_NUM, ECHO_TEST_TXD, ECHO_TEST_RXD, ECHO_TEST_RTS, ECHO_TEST_CTS));

    // Configure a temporary buffer for the incoming data
    uint8_t *data_out = (uint8_t *) malloc(BUF_SIZE);
    uint8_t *data_in = (uint8_t *) malloc(BUF_SIZE);
    // packet.start_code = FINGERPRINT_STARTCODE;
    // packet.type = FINGERPRINT_COMMANDPACKET;
    // packet.length = 0x0008;
    // packet.address[0] = 0xFF;
    // packet.address[1] = 0xFF;
    // packet.address[2] = 0xFF;
    // packet.address[3] = 0xFF;
    // //packet.data = {0x1, 0x0, 0x7, 0x35, 0x1, 0x64, 0x2, 0x0, 0x0, 0xA4};
    // packet.data[0] = 0x1;
    // packet.data[1] = 0x0;
    // packet.data[2] = 0x7;
    // packet.data[3] = 0x35;
    // packet.data[4] = 0x1;
    // packet.data[5] = 0x64;
    // packet.data[6] = 0x2;
    // packet.data[7] = 0x0;
    // packet.data[8] = 0x0;
    // packet.data[9] = 0xA4;



    while (1) {
        //send_packet(&packet);
        //Initialize 16 bytes of data to send
        data_out[0] = FINGERPRINT_STARTCODE >> 8;
        data_out[1] = FINGERPRINT_STARTCODE & 0xFF;
        data_out[2] = 0xFF;
        data_out[3] = 0xFF;
        data_out[4] = 0xFF;  // Add 4 more bytes to make it 8 total
        data_out[5] = 0xFF;
        data_out[6] = 0x1; //0x1;
        data_out[7] = 0x0;
        data_out[8] = 0x7;
        data_out[9] = 0x35;
        data_out[10] = 0x1;
        data_out[11] = 0x64;
        data_out[12] = 0x2;
        data_out[13] = 0x0;
        data_out[14] = 0x0;
        data_out[15] = calculate_checksum(&packet);
        printf("Checksum: %02x\n", data_out[15]);

        //Write only the 8 bytes we want to send
        uart_write_bytes(ECHO_UART_PORT_NUM, (const char *) data_out, DATA_SIZE);
        vTaskDelay(1000 / portTICK_PERIOD_MS);

        // Read only the 8 bytes we expect to receive
        int len = uart_read_bytes(ECHO_UART_PORT_NUM, data_in, DATA_SIZE, 20 / portTICK_PERIOD_MS);
        // Print the received data
        if (len > 0) {
            printf("Received %d bytes: ", len);
            for(int i = 0; i < len; i++) {
                printf("%02x ", data_in[i]);
            }
            printf("\n");
        } else {
            printf("No data received\n");
        }
        vTaskDelay(1000 / portTICK_PERIOD_MS);
        // uart_write_bytes(ECHO_UART_PORT_NUM, (const char *) data, len);
        // if (len) {
        //     data[len] = '\0';
        //     ESP_LOGI(TAG, "Recv str: %s", (char *) data);
        //
    }
}

void app_main(void)
{
    xTaskCreate(echo_task, "uart_echo_task", ECHO_TASK_STACK_SIZE, NULL, 10, NULL);
}
